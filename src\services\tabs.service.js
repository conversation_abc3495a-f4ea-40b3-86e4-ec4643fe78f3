import http from './http';

export const getAllSlots = (kb_id, token) => {
  return http.get(import.meta.env.VITE_APP_BASE_API + 'tabs/slots/all-slots', {
    params: {
      kb_id,
      token
    },
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

export const getQuickLinks = (kb_id, slot_id) => {
  return http.get(import.meta.env.VITE_APP_BASE_API + 'tabs/slots/quick-links/' + slot_id,
    {
      params: {
        kb_id
      }
    },
    {
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};

export const createQuickLink = (data) => {
  return http.post(import.meta.env.VITE_APP_BASE_API + 'tabs/slots/quick-links', data, {
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

export const updateQuickLink = (slot_id, data) => {
  return http.patch(import.meta.env.VITE_APP_BASE_API + 'tabs/slots/quick-links/' + slot_id, data, {
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

export const deleteQuickLink = (slot_id, kb_id) => {
  return http.delete(import.meta.env.VITE_APP_BASE_API + 'tabs/slots/quick-links/' + slot_id, {
    params: {
      kb_id
    },
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

export const getMetaLinks = (kb_id, slot_id) => {
  return http.get(import.meta.env.VITE_APP_BASE_API + 'tabs/slots/meta-links/' + slot_id, {
    params: {
      kb_id
    }
  },
    {
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};

export const createMetaLink = (data) => {
  return http.post(import.meta.env.VITE_APP_BASE_API + 'tabs/slots/meta-links', data, {
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

export const updateMetaLink = (slot_id, data) => {
  return http.patch(import.meta.env.VITE_APP_BASE_API + 'tabs/slots/meta-links/' + slot_id, data, {
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

export const deleteMetaLink = (slot_id, kb_id) => {
  return http.delete(import.meta.env.VITE_APP_BASE_API + 'tabs/slots/meta-links/' + slot_id, {
    params: {
      kb_id
    },
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

export const getLinkGroups = (kb_id, slot_id) => {
  return http.get(import.meta.env.VITE_APP_BASE_API + 'tabs/slots/link-groups/' + slot_id, {
    params: {
      kb_id
    },
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

export const createLinkGroup = (data) => {
  return http.post(import.meta.env.VITE_APP_BASE_API + 'tabs/slots/link-groups', data, {
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

export const updateLinkGroup = (slot_id, data) => {
  return http.patch(import.meta.env.VITE_APP_BASE_API + 'tabs/slots/link-groups/' + slot_id, data, {
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

export const deleteLinkGroup = (slot_id, kb_id) => {
  return http.delete(import.meta.env.VITE_APP_BASE_API + 'tabs/slots/link-groups/' + slot_id, {
    params: {
      kb_id
    },
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

export const getVideos = (kb_id, slot_id) => {
  return http.get(import.meta.env.VITE_APP_BASE_API + 'tabs/slots/videos/' + slot_id, {
    params: {
      kb_id
    },
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

export const createVideo = (data) => {
  return http.post(import.meta.env.VITE_APP_BASE_API + 'tabs/slots/videos', data, {
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

export const updateVideo = (slot_id, data) => {
  return http.patch(import.meta.env.VITE_APP_BASE_API + 'tabs/slots/videos/' + slot_id, data, {
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

export const deleteVideo = (slot_id, kb_id) => {
  return http.delete(import.meta.env.VITE_APP_BASE_API + 'tabs/slots/videos/' + slot_id, {
    params: {
      kb_id
    },
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

export const getTabsPage = (kb_id) => {
  return http.get(import.meta.env.VITE_APP_BASE_API + 'tabs/page', {
    params: {
      kb_id
    },
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

export const saveTabs = (kb_id, data) => {
  return http.post(import.meta.env.VITE_APP_BASE_API + 'tabs/page', data, {
    params: {
      kb_id
    },
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

export const deleteSlider = (slot_id, kb_id) => {
  return http.delete(import.meta.env.VITE_APP_BASE_API + 'tabs/slots/sliders/' + slot_id, {
    params: {
      kb_id
    },
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

export const getMetaData = (link_id) => {
  return http.post(import.meta.env.VITE_APP_BASE_API + 'tabs/metadata', {},
    {
      params: {
        url: link_id
      },
      headers: {
        'Content-Type': 'application/json'
    }
  });
}


export const getThumbnail = (url) => {
  return http.post(import.meta.env.VITE_APP_BASE_API + 'tabs/video-thumbnail', {}, {
      params: {
        url
      }
    },
    {
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
}

// Upload image for slots
export const uploadSlotImage = (kb_id, file) => {
  const formData = new FormData();
  formData.append('file', file);

  return http.post(import.meta.env.VITE_APP_BASE_API + 'tabs/slots/upload/image', formData, {
    params: {
      kb_id
    },
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

// Upload video for slots
export const uploadSlotVideo = (kb_id, file) => {
  const formData = new FormData();
  formData.append('file', file);

  return http.post(import.meta.env.VITE_APP_BASE_API + 'tabs/slots/upload/video', formData, {
    params: {
      kb_id
    },
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

// Create image slot
export const createImageSlot = (data) => {
  return http.post(import.meta.env.VITE_APP_BASE_API + 'tabs/slots/images', data, {
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

// Update image slot
export const updateImageSlot = (slot_id, data) => {
  return http.patch(import.meta.env.VITE_APP_BASE_API + 'tabs/slots/images/' + slot_id, data, {
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

// Delete image slot
export const deleteImageSlot = (slot_id, kb_id) => {
  return http.delete(import.meta.env.VITE_APP_BASE_API + 'tabs/slots/images/' + slot_id, {
    params: {
      kb_id
    },
    headers: {
      'Content-Type': 'application/json'
    }
  });
};
