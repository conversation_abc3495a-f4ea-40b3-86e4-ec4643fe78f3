import { useState, useRef } from 'react';
import { useParams } from 'react-router-dom';
import DInput from '@/components/Global/DInput/DInput';
import DButton from '@/components/Global/DButton';
import DUpload from '@/components/Global/DUpload';
import DUploadImage from '@/components/Global/DUploadImage';
import DSwitch from '@/components/Global/DSwitch';
import DSpinner from '@/components/Global/DSpinner';
import { uploadSlotVideo, uploadSlotImage } from '@/services/tabs.service';
import { v4 as uuidv4 } from 'uuid';

const VideoUploadForm = ({ setIsSaving, closeModal, item, slots, setSlots, chatbotId }) => {
  const params = useParams();
  const [videoTitle, setVideoTitle] = useState(item?.title ?? '');
  const [videoDescription, setVideoDescription] = useState(item?.description ?? '');
  const [videoFile, setVideoFile] = useState(null);
  const [videoUrl, setVideoUrl] = useState(item?.video_url ?? '');
  const [showThumbnail, setShowThumbnail] = useState(item?.show_thumbnail ?? false);
  const [thumbnailFile, setThumbnailFile] = useState(null);
  const [thumbnailUrl, setThumbnailUrl] = useState(item?.thumbnail_url ?? '');
  const [error, setError] = useState({});
  const [submitted, setSubmitted] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const videoRef = useRef(null);

  const validateForm = () => {
    let errors = {};

    if (videoTitle === '') {
      errors.videoTitle = 'Video title is required';
    }
    if (!videoFile && !videoUrl) {
      errors.videoFile = 'Video is required';
    }
    if (showThumbnail && !thumbnailFile && !thumbnailUrl) {
      errors.thumbnailFile = 'Thumbnail is required when enabled';
    }

    setError(errors);
    return Object.keys(errors).length === 0;
  };

  const handleVideoChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['video/mp4', 'video/webm', 'video/ogg', 'video/avi', 'video/mov'];
      if (!allowedTypes.includes(file.type)) {
        setError({ ...error, videoFile: 'Please upload MP4, WebM, OGG, AVI, or MOV files only.' });
        return;
      }
      
      // Validate file size (100MB max)
      if (file.size > 100 * 1024 * 1024) {
        setError({ ...error, videoFile: 'Video size must be less than 100MB' });
        return;
      }

      setVideoFile(file);
      setError({ ...error, videoFile: '' });
    }
  };

  const handleThumbnailChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];
      if (!allowedTypes.includes(file.type)) {
        setError({ ...error, thumbnailFile: 'Please upload JPEG, PNG, or JPG files only.' });
        return;
      }
      
      // Validate file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        setError({ ...error, thumbnailFile: 'Thumbnail size must be less than 5MB' });
        return;
      }

      setThumbnailFile(file);
      setError({ ...error, thumbnailFile: '' });
    }
  };

  const uploadVideo = async () => {
    if (!videoFile) return videoUrl;
    
    try {
      setIsUploading(true);
      const response = await uploadSlotVideo(params.id ?? chatbotId, videoFile);
      if (response.status === 200) {
        return response.data.url;
      }
      throw new Error('Upload failed');
    } catch (error) {
      console.error('Error uploading video:', error);
      setError({ ...error, videoFile: 'Failed to upload video' });
      return null;
    }
  };

  const uploadThumbnail = async () => {
    if (!thumbnailFile) return thumbnailUrl;
    
    try {
      const response = await uploadSlotImage(params.id ?? chatbotId, thumbnailFile);
      if (response.status === 200) {
        return response.data.url;
      }
      throw new Error('Thumbnail upload failed');
    } catch (error) {
      console.error('Error uploading thumbnail:', error);
      setError({ ...error, thumbnailFile: 'Failed to upload thumbnail' });
      return null;
    }
  };

  const handleSubmit = async () => {
    setSubmitted(true);
    if (!validateForm()) return;

    setIsSaving(true);
    
    const uploadedVideoUrl = await uploadVideo();
    if (!uploadedVideoUrl) {
      setIsSaving(false);
      setIsUploading(false);
      return;
    }

    let uploadedThumbnailUrl = thumbnailUrl;
    if (showThumbnail && thumbnailFile) {
      uploadedThumbnailUrl = await uploadThumbnail();
      if (!uploadedThumbnailUrl) {
        setIsSaving(false);
        setIsUploading(false);
        return;
      }
    }

    const payload = {
      kb_id: params.id ?? chatbotId,
      title: videoTitle,
      description: videoDescription,
      video_url: uploadedVideoUrl,
      show_thumbnail: showThumbnail,
      thumbnail_url: uploadedThumbnailUrl,
      order: slots.length + 1,
      frontend_id: uuidv4(),
    };

    setSlots((prevSlots) => [...prevSlots, { ...payload, type: 'video_upload', disclaimer: 'Video Upload' }]);
    setIsUploading(false);
    closeModal();
  };

  const handleUpdate = async () => {
    setSubmitted(true);
    if (!validateForm()) return;

    setIsSaving(true);

    let finalVideoUrl = videoUrl;
    if (videoFile) {
      finalVideoUrl = await uploadVideo();
      if (!finalVideoUrl) {
        setIsSaving(false);
        setIsUploading(false);
        return;
      }
    }

    let finalThumbnailUrl = thumbnailUrl;
    if (showThumbnail && thumbnailFile) {
      finalThumbnailUrl = await uploadThumbnail();
      if (!finalThumbnailUrl) {
        setIsSaving(false);
        setIsUploading(false);
        return;
      }
    }

    setSlots((prevSlots) => 
      prevSlots.map(slot => 
        (slot.frontend_id && item.frontend_id ? slot.frontend_id === item.frontend_id : slot.id === item.id) ? { 
          ...slot, 
          title: videoTitle,
          description: videoDescription,
          video_url: finalVideoUrl,
          show_thumbnail: showThumbnail,
          thumbnail_url: finalThumbnailUrl,
          order: item.order,
          disclaimer: 'Video Upload',
          frontend_id: item.frontend_id,
        } : slot
      )
    );

    setIsUploading(false);
    closeModal();
    setIsSaving(false);
  };

  return (
    <div className="flex flex-col gap-size5">
      <div className="flex flex-col gap-size1">
        <p className="text-base font-medium tracking-tight">Video title</p>
        <DInput 
          placeholder="Enter video title" 
          value={videoTitle} 
          onChange={(e) => setVideoTitle(e.target.value)} 
          error={submitted ? error['videoTitle'] : ''}
        />
      </div>
      
      <div className="flex flex-col gap-size1">
        <p className="text-base font-medium tracking-tight">Video description</p>
        <DInput 
          placeholder="Enter video description" 
          minRows={3} 
          value={videoDescription} 
          onChange={(e) => setVideoDescription(e.target.value)} 
          error={submitted ? error['videoDescription'] : ''}
        />
      </div>

      <div className="flex flex-col gap-size1">
        <p className="text-base font-medium tracking-tight">Upload Video</p>
        <div 
          className="cursor-pointer"
          onClick={() => videoRef.current?.click()}
        >
          <DUpload
            title={videoFile ? videoFile.name : "Upload or drop video file"}
            subtitle="MP4, WebM, OGG, AVI, MOV supported"
            note="Max. size 100MB"
            onChangeFile={handleVideoChange}
            accept="video/*"
          />
        </div>
        <input
          ref={videoRef}
          type="file"
          className="hidden"
          onChange={handleVideoChange}
          accept="video/*"
        />
        {submitted && error['videoFile'] && (
          <span className="text-xs text-red-500">{error['videoFile']}</span>
        )}
      </div>

      <div className="flex items-center gap-size1 h-[30px]">
        <DSwitch 
          label="Show thumbnail" 
          checked={showThumbnail} 
          onChange={(checked) => setShowThumbnail(checked)} 
        />
        {isUploading && <DSpinner />}
      </div>

      {showThumbnail && (
        <div className="flex flex-col gap-size1">
          <p className="text-base font-medium tracking-tight">Upload Thumbnail</p>
          <DUploadImage
            label="Upload Thumbnail"
            handleImageChange={handleThumbnailChange}
            imageUrl={thumbnailFile ? URL.createObjectURL(thumbnailFile) : thumbnailUrl}
            imageFile={thumbnailFile}
            error={submitted ? error['thumbnailFile'] : ''}
            maxSize={5 * 1024 * 1024} // 5MB
            ratioWith={16}
            ratioHeight={9}
            maxWidth={400}
            maxHeight={225}
          />
        </div>
      )}

      {item && Object.keys(item).length > 0 ? (
        <DButton 
          variant="dark" 
          onClick={handleUpdate} 
          fullWidth 
          disabled={isUploading}
        >
          {isUploading ? 'Uploading...' : 'Update'}
        </DButton>
      ) : (
        <DButton 
          variant="dark" 
          onClick={handleSubmit} 
          fullWidth 
          disabled={isUploading}
        >
          {isUploading ? 'Uploading...' : 'Complete'}
        </DButton>
      )}
    </div>
  );
};

export default VideoUploadForm;
