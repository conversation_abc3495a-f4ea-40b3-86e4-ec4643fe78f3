import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import DInput from '@/components/Global/DInput/DInput';
import DButton from '@/components/Global/DButton';
import DUploadImage from '@/components/Global/DUploadImage';
import { uploadSlotImage } from '@/services/tabs.service';
import { v4 as uuidv4 } from 'uuid';
import { ACCEPTED_IMAGE_TYPES } from '@/constants';

const ImageUploadForm = ({ setIsSaving, closeModal, item, slots, setSlots, chatbotId }) => {
  const params = useParams();
  const [imageTitle, setImageTitle] = useState(item?.title ?? '');
  const [imageDescription, setImageDescription] = useState(item?.description ?? '');
  const [imageFile, setImageFile] = useState(null);
  const [imageUrl, setImageUrl] = useState(item?.image_url ?? '');
  const [error, setError] = useState({});
  const [submitted, setSubmitted] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  const validateForm = () => {
    let errors = {};

    if (imageTitle === '') {
      errors.imageTitle = 'Image title is required';
    }
    if (!imageFile && !imageUrl) {
      errors.imageFile = 'Image is required';
    }

    setError(errors);
    return Object.keys(errors).length === 0;
  };

  const handleImageChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      if (!ACCEPTED_IMAGE_TYPES.includes(file.type)) {
        setError({ ...error, imageFile: 'Please upload PNG, JPEG, JPG, or SVG files only.' });
        return;
      }
      
      // Validate file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        setError({ ...error, imageFile: 'Image size must be less than 5MB' });
        return;
      }

      setImageFile(file);
      setError({ ...error, imageFile: '' });
    }
  };

  const uploadImage = async () => {
    if (!imageFile) return imageUrl;
    
    try {
      setIsUploading(true);
      const response = await uploadSlotImage(params.id ?? chatbotId, imageFile);
      if (response.status === 200) {
        return response.data.url;
      }
      throw new Error('Upload failed');
    } catch (error) {
      console.error('Error uploading image:', error);
      setError({ ...error, imageFile: 'Failed to upload image' });
      return null;
    } finally {
      setIsUploading(false);
    }
  };

  const handleSubmit = async () => {
    setSubmitted(true);
    if (!validateForm()) return;

    setIsSaving(true);
    
    const uploadedImageUrl = await uploadImage();
    if (!uploadedImageUrl) {
      setIsSaving(false);
      return;
    }

    const payload = {
      kb_id: params.id ?? chatbotId,
      title: imageTitle,
      description: imageDescription,
      image_url: uploadedImageUrl,
      order: slots.length + 1,
      frontend_id: uuidv4(),
    };

    setSlots((prevSlots) => [...prevSlots, { ...payload, type: 'image', disclaimer: 'Image' }]);
    closeModal();
  };

  const handleUpdate = async () => {
    setSubmitted(true);
    if (!validateForm()) return;

    setIsSaving(true);

    let finalImageUrl = imageUrl;
    if (imageFile) {
      finalImageUrl = await uploadImage();
      if (!finalImageUrl) {
        setIsSaving(false);
        return;
      }
    }

    setSlots((prevSlots) => 
      prevSlots.map(slot => 
        (slot.frontend_id && item.frontend_id ? slot.frontend_id === item.frontend_id : slot.id === item.id) ? { 
          ...slot, 
          title: imageTitle,
          description: imageDescription,
          image_url: finalImageUrl,
          order: item.order,
          disclaimer: 'Image',
          frontend_id: item.frontend_id,
        } : slot
      )
    );

    closeModal();
    setIsSaving(false);
  };

  return (
    <div className="flex flex-col gap-size5">
      <div className="flex flex-col gap-size1">
        <p className="text-base font-medium tracking-tight">Image title</p>
        <DInput 
          placeholder="Enter image title" 
          value={imageTitle} 
          onChange={(e) => setImageTitle(e.target.value)} 
          error={submitted ? error['imageTitle'] : ''}
        />
      </div>
      
      <div className="flex flex-col gap-size1">
        <p className="text-base font-medium tracking-tight">Image description</p>
        <DInput 
          placeholder="Enter image description" 
          minRows={3} 
          value={imageDescription} 
          onChange={(e) => setImageDescription(e.target.value)} 
          error={submitted ? error['imageDescription'] : ''}
        />
      </div>

      <div className="flex flex-col gap-size1">
        <p className="text-base font-medium tracking-tight">Upload Image</p>
        <DUploadImage
          label="Upload Image"
          handleImageChange={handleImageChange}
          imageUrl={imageFile ? URL.createObjectURL(imageFile) : imageUrl}
          imageFile={imageFile}
          error={submitted ? error['imageFile'] : ''}
          maxSize={5 * 1024 * 1024} // 5MB
          ratioWith={16}
          ratioHeight={9}
          maxWidth={800}
          maxHeight={450}
        />
        <span className="text-xs text-grey-20 font-light">
          *Supported formats: PNG, JPEG, JPG, SVG • Max size: 5MB
        </span>
      </div>

      {item && Object.keys(item).length > 0 ? (
        <DButton 
          variant="dark" 
          onClick={handleUpdate} 
          fullWidth 
          disabled={isUploading}
        >
          {isUploading ? 'Uploading...' : 'Update'}
        </DButton>
      ) : (
        <DButton 
          variant="dark" 
          onClick={handleSubmit} 
          fullWidth 
          disabled={isUploading}
        >
          {isUploading ? 'Uploading...' : 'Complete'}
        </DButton>
      )}
    </div>
  );
};

export default ImageUploadForm;
