import { useEffect, useState } from 'react';

import DAlert from '../../Global/DAlert';
import DButton from '../../Global/DButton';
import DCheckbox from '../../Global/DCheckbox';
import DInput from '../../Global/DInput/DInput';
import DSwitch from '../../Global/DSwitch';
import DUploadImage from '../../Global/DUploadImage';
import LinkIcon from '../../Global/Icons/LinkIcon';
import { useParams } from 'react-router-dom';
import { getMetaData, uploadSlotImage } from '../../../services/tabs.service';
import DSpinner from '@/components/Global/DSpinner';
import useDebounce from '@/hooks/useDebounce';
import { v4 as uuidv4 } from 'uuid';
import { ACCEPTED_IMAGE_TYPES } from '@/constants';

const QuickLinkForm = ({
  pullMetaDataOption = false,
  setIsSaving,
  closeModal,
  item,
  setSlots,
  slots,
  chatbotId,
  type,
}) => {
  const params = useParams();
  const [linkTitle, setLinkTitle] = useState(item?.title ?? '');
  const [linkUrl, setLinkUrl] = useState(item?.url ?? 'https://');
  const [openInNewTab, setOpenInNewTab] = useState(
    item?.open_in_new_tab ?? true
  );
  const [pullMetaData, setPullMetaData] = useState(
    item?.pull_meta_data ?? pullMetaDataOption
  );
  const [metaDescription, setMetaDescription] = useState(
    item?.description ?? ''
  );
  const [metaImageUrl, setMetaImageUrl] = useState(
    item?.image_url ?? 'https://'
  );
  const [metaImageFile, setMetaImageFile] = useState(null);
  const [useImageUpload, setUseImageUpload] = useState(false);
  const [error, setError] = useState({
    title: null,
    url: null,
    metaDescription: null,
    metaImageUrl: null,
    metaImageFile: null,
  });
  const [submitted, setSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [pullMetaDataMetaLink, setPullMetaDataMetaLink] = useState(true);

  const validateForm = () => {
    const errors = {};

    // Validate link title
    if (linkTitle.trim() === '') {
      errors.title = 'Link title is required';
    }

    // Validate link URL
    if (linkUrl.trim() === '') {
      errors.url = 'Link URL is required';
    } else {
      try {
        new URL(linkUrl);
      } catch {
        errors.url = 'Link URL is not a valid URL';
      }
    }

    // Validate meta fields if pullMetaData is enabled
    if (pullMetaData) {
      if (metaDescription.trim() === '') {
        errors.metaDescription =
          'Description is required when meta data is enabled';
      }

      if (useImageUpload) {
        if (!metaImageFile) {
          errors.metaImageFile = 'Image file is required when upload is enabled';
        }
      } else {
        if (metaImageUrl.trim() === '') {
          errors.metaImageUrl = 'Image URL is required when meta data is enabled';
        } else {
          try {
            new URL(metaImageUrl);
          } catch {
            errors.metaImageUrl = 'Image URL is not a valid URL';
          }
        }
      }
    }

    setError(errors);
    return Object.keys(errors).length === 0;
  };

  const uploadMetaImage = async () => {
    if (!metaImageFile) return metaImageUrl;

    try {
      const response = await uploadSlotImage(params.id ?? chatbotId, metaImageFile);
      if (response.status === 200) {
        return response.data.url;
      }
      throw new Error('Upload failed');
    } catch (error) {
      console.error('Error uploading image:', error);
      setError({ ...error, metaImageFile: 'Failed to upload image' });
      return null;
    }
  };

  const handleSubmit = async () => {
    setSubmitted(true);
    if (!validateForm()) return;

    setIsSaving(true);
    try {
      let finalImageUrl = metaImageUrl;
      if (pullMetaData && useImageUpload && metaImageFile) {
        finalImageUrl = await uploadMetaImage();
        if (!finalImageUrl) {
          setIsSaving(false);
          return;
        }
      }

      const payload = {
        kb_id: params.id ?? chatbotId,
        title: linkTitle,
        url: linkUrl,
        open_in_new_tab: openInNewTab,
        pull_meta_data: pullMetaData,
        order: slots.length + 1,
        frontend_id: uuidv4(),
      };

      if (pullMetaData) {
        payload.description = metaDescription;
        payload.image_url = finalImageUrl;
      }

      setSlots((prevSlots) => [
        ...prevSlots,
        {
          ...payload,
          type: pullMetaData ? 'meta_link' : 'quick_link',
          disclaimer: pullMetaData ? 'Meta link' : 'Quick link',
        },
      ]);

      // const response = pullMetaData
      //   ? await slotsService.createMetaLink(payload)
      //   : await slotsService.createQuickLink(payload);

      // if (response.status === 200) {
      setIsSaving(false);
      closeModal();
      //   refetch();
      // }
    } catch (error) {
      console.error('Error creating link:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleUpdate = async () => {
    setSubmitted(true);
    if (!validateForm()) return;

    setIsSaving(true);

    let finalImageUrl = metaImageUrl;
    if (pullMetaData && useImageUpload && metaImageFile) {
      finalImageUrl = await uploadMetaImage();
      if (!finalImageUrl) {
        setIsSaving(false);
        return;
      }
    }

    //set the slot where id is item.id
    setSlots((prevSlots) =>
      prevSlots.map((slot) =>
        (
          slot.frontend_id && item.frontend_id
            ? slot.frontend_id === item.frontend_id
            : slot.id === item.id
        )
          ? {
              ...slot,
              title: linkTitle,
              url: linkUrl,
              open_in_new_tab: openInNewTab,
              pull_meta_data: pullMetaData,
              order: item.order,
              description: metaDescription,
              image_url: finalImageUrl,
              disclaimer: pullMetaData ? 'Meta link' : 'Quick link',
              type: pullMetaData ? 'meta_link' : 'quick_link',
            }
          : slot
      )
    );

    // try {
    //   const response = await slotsService.updateQuickLink(item.id, {
    //     kb_id: params.id,
    //     title: linkTitle,
    //     url: linkUrl,
    //     open_in_new_tab: openInNewTab,
    //     pull_meta_data: pullMetaData
    //   });
    //   if (response.status === 200) {
    setIsSaving(false);
    closeModal();
    //     refetch();
    //   }
    // } catch (error) {
    //   console.error("Error updating link:", error);
    // } finally {
    //   setIsSaving(false);
    // }
  };

  const handlePullMetaDataChange = async (isToogleOn = null) => {
    if (linkUrl.trim() === '') {
      setError({
        ...error,
        url: 'Link URL is required',
      });
      return;
    }
    if (type === 'quick_link' && pullMetaData) {
      setPullMetaData(!pullMetaData);
      return;
    }

    if (type === 'meta_link' && !isToogleOn) {
      setPullMetaDataMetaLink(!pullMetaDataMetaLink);
      setPullMetaData(!pullMetaData);
      return;
    }

    try {
      setIsLoading(true);
      const response = await getMetaData(linkUrl);
      if (response.status === 200) {
        setPullMetaDataMetaLink(true);
        setPullMetaData(true);
        setIsLoading(false);
        if (response.data.description) {
          setMetaDescription(response.data.description);
        }
        if (response.data.image_url) {
          setMetaImageUrl(response.data.image_url);
        }
      }
    } catch (error) {
      console.error('Error updating link:', error);
      setIsLoading(false);
    }
  };

  const debouncedFetchMetadata = useDebounce(() => {
    // Check if URL is valid and not empty
    try {
      const url = new URL(linkUrl);
      if (pullMetaDataOption && linkUrl.trim()) {
        handlePullMetaDataChange(
          type === 'meta_link' ? pullMetaDataMetaLink : null
        );
      }
    } catch {
      setError({
        ...error,
        url: 'Link URL is not a valid URL',
      });
    }
  }, 2000);

  useEffect(() => {
    if (type === 'meta_link') {
      debouncedFetchMetadata();
    }
  }, [linkUrl]);

  const handleImageChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      if (!ACCEPTED_IMAGE_TYPES.includes(file.type)) {
        setError({ ...error, metaImageFile: 'Please upload PNG, JPEG, JPG, or SVG files only.' });
        return;
      }

      // Validate file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        setError({ ...error, metaImageFile: 'Image size must be less than 5MB' });
        return;
      }

      setMetaImageFile(file);
      setError({ ...error, metaImageFile: '' });
    }
  };

  return (
    <div className="flex flex-col gap-size5">
      <div className="flex flex-col gap-size2">
        <div className="flex flex-col gap-size1 pb-2">
          <p className="text-base font-medium tracking-tight">Link title</p>
          <DInput
            placeholder="Enter link title"
            value={linkTitle}
            onChange={(e) => setLinkTitle(e.target.value)}
            error={submitted ? error.title : ''}
          />
        </div>
        <div className="flex flex-col gap-size1 pb-2">
          <p className="text-base font-medium tracking-tight">Link URL</p>
          <DInput
            placeholder="Enter link URL"
            value={linkUrl}
            onChange={(e) => setLinkUrl(e.target.value)}
            icon={<LinkIcon />}
            error={submitted ? error.url : ''}
          />
        </div>
        <div>
          <DCheckbox
            label="Open in new tab"
            checked={openInNewTab}
            onChange={() => {
              setOpenInNewTab(!openInNewTab);
              setIsSaving(true);
            }}
          />
        </div>
      </div>
      <div className="h-[1px] w-full bg-grey-5"></div>
      <div className="flex flex-col gap-size2">
        <div className="flex items-center gap-size1 h-[30px]">
          <DSwitch
            label="Pull meta data"
            checked={pullMetaData}
            onChange={() => {
              if (type === 'meta_link') {
                setPullMetaDataMetaLink(!pullMetaDataMetaLink);
              }
              handlePullMetaDataChange(
                type === 'meta_link' ? !pullMetaDataMetaLink : null
              );
            }}
          />
          {isLoading && <DSpinner />}
        </div>
        <DAlert
          state="info"
          classNamesWrapper="text-xs font-regular tracking-tight"
        >
          If toggled on, this quick link will become a "Meta link".
        </DAlert>
        {pullMetaData && (
          <div className="flex flex-col gap-size1">
            <p className="text-base font-medium tracking-tight">Description</p>
            <DInput
              placeholder="Enter description"
              value={metaDescription}
              onChange={(e) => setMetaDescription(e.target.value)}
              minRows={3}
              error={submitted ? error.metaDescription : ''}
            />
          </div>
        )}
        {pullMetaData && (
          <div className="flex flex-col gap-size2">
            <div className="flex items-center gap-size1">
              <DSwitch
                label="Upload image instead of URL"
                checked={useImageUpload}
                onChange={(checked) => setUseImageUpload(checked)}
              />
            </div>

            {useImageUpload ? (
              <div className="flex flex-col gap-size1">
                <p className="text-base font-medium tracking-tight">Upload Image</p>
                <DUploadImage
                  label="Upload Image"
                  handleImageChange={handleImageChange}
                  imageUrl={metaImageFile ? URL.createObjectURL(metaImageFile) : ''}
                  imageFile={metaImageFile}
                  error={submitted ? error.metaImageFile : ''}
                  maxSize={5 * 1024 * 1024} // 5MB
                  ratioWith={16}
                  ratioHeight={9}
                  maxWidth={400}
                  maxHeight={225}
                />
              </div>
            ) : (
              <div className="flex flex-col gap-size1">
                <p className="text-base font-medium tracking-tight">Image URL</p>
                <DInput
                  placeholder="Enter image URL"
                  value={metaImageUrl}
                  onChange={(e) => setMetaImageUrl(e.target.value)}
                  error={submitted ? error.metaImageUrl : ''}
                />
              </div>
            )}
          </div>
        )}
      </div>
      {item && Object.keys(item).length > 0 ? (
        <DButton variant="dark" onClick={handleUpdate} fullWidth>
          Update
        </DButton>
      ) : (
        <DButton variant="dark" onClick={handleSubmit} fullWidth>
          Complete
        </DButton>
      )}
    </div>
  );
};

export default QuickLinkForm;
