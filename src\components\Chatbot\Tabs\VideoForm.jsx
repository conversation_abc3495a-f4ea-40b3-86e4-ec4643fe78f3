import { useState, useEffect, useRef } from 'react';
import { useParams } from 'react-router-dom';
import LinkIcon from '@/components/Global/Icons/LinkIcon';
import DInput from '@/components/Global/DInput/DInput';
import DSwitch from '@/components/Global/DSwitch';
import DButton from '@/components/Global/DButton';
import DSpinner from '@/components/Global/DSpinner';
import DUpload from '@/components/Global/DUpload';
import DUploadImage from '@/components/Global/DUploadImage';
import { getThumbnail, uploadSlotVideo, uploadSlotImage } from '@/services/tabs.service';
import { v4 as uuidv4 } from 'uuid';

const VideoForm = ({ setIsSaving, closeModal, item, slots, setSlots, chatbotId }) => {
  const params = useParams();
  const [videoUrl, setVideoUrl] = useState(item?.url ?? 'https://');
  const [videoTitle, setVideoTitle] = useState(item?.title ?? '');
  const [videoDescription, setVideoDescription] = useState(item?.description ?? '');
  const [showThumbnail, setShowThumbnail] = useState(item?.show_thumbnail ?? false);
  const [thumbnailUrl, setThumbnailUrl] = useState(item?.thumbnail_url ?? 'https://');
  const [useVideoUpload, setUseVideoUpload] = useState(false);
  const [videoFile, setVideoFile] = useState(null);
  const [useThumbnailUpload, setUseThumbnailUpload] = useState(false);
  const [thumbnailFile, setThumbnailFile] = useState(null);
  const [error, setError] = useState({});
  const [submitted, setSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const videoRef = useRef(null);

  const validateForm = () => {
    let errors = {};

    if (useVideoUpload) {
      if (!videoFile) {
        errors.videoFile = 'Video file is required';
      }
    } else {
      if (videoUrl === '') {
        errors.videoUrl = 'Video URL is required';
      }
    }

    if (videoTitle === '') {
      errors.videoTitle = 'Video title is required';
    }

    if (showThumbnail) {
      if (useThumbnailUpload) {
        if (!thumbnailFile) {
          errors.thumbnailFile = 'Thumbnail file is required';
        }
      } else {
        if (thumbnailUrl === '') {
          errors.thumbnailUrl = 'Thumbnail URL is required';
        }
      }
    }

    setError(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async () => {
    setSubmitted(true);
    if (!validateForm()) return;

    setIsSaving(true);

    let finalVideoUrl = videoUrl;
    if (useVideoUpload && videoFile) {
      finalVideoUrl = await uploadVideo();
      if (!finalVideoUrl) {
        setIsSaving(false);
        return;
      }
    }

    let finalThumbnailUrl = thumbnailUrl;
    if (showThumbnail && useThumbnailUpload && thumbnailFile) {
      finalThumbnailUrl = await uploadThumbnailImage();
      if (!finalThumbnailUrl) {
        setIsSaving(false);
        return;
      }
    }

    const payload = {
      kb_id: params.id ?? chatbotId,
      url: finalVideoUrl,
      title: videoTitle,
      description: videoDescription,
      show_thumbnail: showThumbnail,
      thumbnail_url: finalThumbnailUrl,
      order: slots.length + 1,
      frontend_id: uuidv4(),
    }

    setSlots((prevSlots) => [...prevSlots, { ...payload, type: 'video', disclaimer: 'Video' }]);
    closeModal();
  };

  const handleUpdate = async () => {
    setSubmitted(true);
    if (!validateForm()) return;

    setIsSaving(true);

    let finalVideoUrl = videoUrl;
    if (useVideoUpload && videoFile) {
      finalVideoUrl = await uploadVideo();
      if (!finalVideoUrl) {
        setIsSaving(false);
        return;
      }
    }

    let finalThumbnailUrl = thumbnailUrl;
    if (showThumbnail && useThumbnailUpload && thumbnailFile) {
      finalThumbnailUrl = await uploadThumbnailImage();
      if (!finalThumbnailUrl) {
        setIsSaving(false);
        return;
      }
    }

    setSlots((prevSlots) =>
      prevSlots.map(slot =>
        (slot.frontend_id && item.frontend_id ? slot.frontend_id === item.frontend_id : slot.id === item.id) ? {
          ...slot,
          url: finalVideoUrl,
          title: videoTitle,
          description: videoDescription,
          show_thumbnail: showThumbnail,
          thumbnail_url: finalThumbnailUrl,
          order: item.order,
          disclaimer: 'Video',
          frontend_id: item.frontend_id,
        } : slot
      )
    );

    closeModal();
    setIsSaving(false);
  };

  const handleGetThumbnail = async (checked) => {
    try{
      setIsLoading(true);
      const response = await getThumbnail(videoUrl);
      if(response.status === 200){
        setThumbnailUrl(response.data.thumbnail_url);
        setShowThumbnail(checked);
        setIsLoading(false);
      }
    }catch(error){
      console.error('Error getting thumbnail:', error);
      setIsLoading(false);
    }
  }

  const handleVideoChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['video/mp4', 'video/webm', 'video/ogg', 'video/avi', 'video/mov'];
      if (!allowedTypes.includes(file.type)) {
        setError({ ...error, videoFile: 'Please upload MP4, WebM, OGG, AVI, or MOV files only.' });
        return;
      }

      // Validate file size (100MB max)
      if (file.size > 100 * 1024 * 1024) {
        setError({ ...error, videoFile: 'Video size must be less than 100MB' });
        return;
      }

      setVideoFile(file);
      setError({ ...error, videoFile: '' });
    }
  };

  const handleThumbnailChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];
      if (!allowedTypes.includes(file.type)) {
        setError({ ...error, thumbnailFile: 'Please upload JPEG, PNG, or JPG files only.' });
        return;
      }

      // Validate file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        setError({ ...error, thumbnailFile: 'Thumbnail size must be less than 5MB' });
        return;
      }

      setThumbnailFile(file);
      setError({ ...error, thumbnailFile: '' });
    }
  };

  const uploadVideo = async () => {
    if (!videoFile) return videoUrl;

    try {
      setIsLoading(true);
      const response = await uploadSlotVideo(params.id ?? chatbotId, videoFile);
      if (response.status === 200) {
        return response.data.url;
      }
      throw new Error('Upload failed');
    } catch (error) {
      console.error('Error uploading video:', error);
      setError({ ...error, videoFile: 'Failed to upload video' });
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  const uploadThumbnailImage = async () => {
    if (!thumbnailFile) return thumbnailUrl;

    try {
      const response = await uploadSlotImage(params.id ?? chatbotId, thumbnailFile);
      if (response.status === 200) {
        return response.data.url;
      }
      throw new Error('Thumbnail upload failed');
    } catch (error) {
      console.error('Error uploading thumbnail:', error);
      setError({ ...error, thumbnailFile: 'Failed to upload thumbnail' });
      return null;
    }
  };

  return (
    <div className="flex flex-col gap-size5">
      <div className="flex flex-col gap-size2">
        <div className="flex items-center gap-size1">
          <DSwitch
            label="Upload video file instead of URL"
            checked={useVideoUpload}
            onChange={(checked) => setUseVideoUpload(checked)}
          />
        </div>

        {useVideoUpload ? (
          <div className="flex flex-col gap-size1">
            <p className="text-base font-medium tracking-tight">Upload Video</p>
            <div
              className="cursor-pointer"
              onClick={() => videoRef.current?.click()}
            >
              <DUpload
                title={videoFile ? videoFile.name : "Upload or drop video file"}
                subtitle="MP4, WebM, OGG, AVI, MOV supported"
                note="Max. size 100MB"
                onChangeFile={handleVideoChange}
                accept="video/*"
              />
            </div>
            <input
              ref={videoRef}
              type="file"
              className="hidden"
              onChange={handleVideoChange}
              accept="video/*"
            />
            {submitted && error['videoFile'] && (
              <span className="text-xs text-red-500">{error['videoFile']}</span>
            )}
          </div>
        ) : (
          <div className="flex flex-col gap-size1">
            <p className="text-base font-medium tracking-tight">Video URL</p>
            <DInput
              placeholder="Enter video URL"
              iconPlacement="pre"
              icon={<LinkIcon />}
              value={videoUrl}
              onChange={(e) => setVideoUrl(e.target.value)}
              error={submitted ? error['videoUrl'] : ''}
            />
            <span className="text-xs text-grey-20 font-light">
              *YouTube, Vimeo or Loom link
            </span>
          </div>
        )}
      </div>
      <div className="flex flex-col gap-size1">
        <p className="text-base font-medium tracking-tight">Video title</p>
        <DInput 
          placeholder="Enter video title" 
          value={videoTitle} 
          onChange={(e) => setVideoTitle(e.target.value)} 
          error={submitted ? error['videoTitle'] : ''}
        />
      </div>
      <div className="flex flex-col gap-size1">
        <p className="text-base font-medium tracking-tight">Video description</p>
        <DInput 
          placeholder="Enter video description" 
          minRows={5} 
          value={videoDescription} 
          onChange={(e) => setVideoDescription(e.target.value)} 
          error={submitted ? error['videoDescription'] : ''}
        />
      </div>
      <div className="flex items-center gap-size1 h-[30px]">
        <DSwitch label="Show thumbnail" checked={showThumbnail}
        onChange={(checked) => {
          if (!useVideoUpload) {
            handleGetThumbnail(checked);
          } else {
            setShowThumbnail(checked);
          }
        }} />
        {isLoading && <DSpinner />}
      </div>

      {showThumbnail && (
        <div className="flex flex-col gap-size2">
          <div className="flex items-center gap-size1">
            <DSwitch
              label="Upload thumbnail instead of URL"
              checked={useThumbnailUpload}
              onChange={(checked) => setUseThumbnailUpload(checked)}
            />
          </div>

          {useThumbnailUpload ? (
            <div className="flex flex-col gap-size1">
              <p className="text-base font-medium tracking-tight">Upload Thumbnail</p>
              <DUploadImage
                label="Upload Thumbnail"
                handleImageChange={handleThumbnailChange}
                imageUrl={thumbnailFile ? URL.createObjectURL(thumbnailFile) : thumbnailUrl}
                imageFile={thumbnailFile}
                error={submitted ? error['thumbnailFile'] : ''}
                maxSize={5 * 1024 * 1024} // 5MB
                ratioWith={16}
                ratioHeight={9}
                maxWidth={400}
                maxHeight={225}
              />
            </div>
          ) : (
            <div className="flex flex-col gap-size1">
              <p className="text-base font-medium tracking-tight">Thumbnail URL</p>
              <DInput
                placeholder="Enter thumbnail URL"
                value={thumbnailUrl}
                onChange={(e) => setThumbnailUrl(e.target.value)}
                error={submitted ? error['thumbnailUrl'] : ''}
              />
            </div>
          )}
        </div>
      )}

      {item && Object.keys(item).length > 0 ? (
        <DButton
          variant="dark"
          onClick={handleUpdate}
          fullWidth
          disabled={isLoading}
        >
          {isLoading ? 'Uploading...' : 'Update'}
        </DButton>
      ) : (
        <DButton
          variant="dark"
          onClick={handleSubmit}
          fullWidth
          disabled={isLoading}
        >
          {isLoading ? 'Uploading...' : 'Complete'}
        </DButton>
      )}
    </div>
  );
};

export default VideoForm;
