import { useState } from 'react';

import {
  CloseButton,
  Dialog,
  DialogBackdrop,
  DialogPanel,
  DialogTitle,
} from '@headlessui/react';

import ChevronLeftIcon from '../../Global/Icons/ChevronLeftIcon';
import CloseIcon from '../../Global/Icons/CloseIcon';
import LinkIcon from '../../Global/Icons/LinkIcon';
import ListIcon from '../../Global/Icons/ListIcon';
import VideoIcon from '../../Global/Icons/VideoIcon';
import ImageIcon from '../../Global/Icons/ImageIcon';
import UploadIcon from '../../Global/Icons/UploadIcon';
import QuickLinkForm from './QuickLinkForm';
import LinkGroupForm from './LinkGroupForm';
import VideoForm from './VideoForm';
import SliderForm from './SliderForm';
import ImageUploadForm from './ImageUploadForm';
import VideoUploadForm from './VideoUploadForm';
import SliderIcon from '@/components/Global/Icons/SliderIcon';

const SlotForm = ({
  isOpen,
  onClose,
  selectedBtn,
  setSelectedBtn,
  item,
  refetch,
  setSlots,
  slots,
  chatbotId,
}) => {
  const [pullMetaData, setPullMetaData] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const handleSelectedBtn = (btn) => {
    setSelectedBtn(btn);
  };

  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <DialogBackdrop className="fixed inset-0 bg-white/90" />
      <div className="fixed inset-0 flex w-screen items-center justify-center p-4">
        <DialogPanel className="w-3/5 md:w-96 bg-white p-size6 rounded-size0 flex flex-col gap-size5 border border-gray-6">
          <header className="flex justify-between items-center">
            {selectedBtn !== 'all_links' && !item && (
              <button
                className="dbutton"
                onClick={() => setSelectedBtn('all_links')}
              >
                <ChevronLeftIcon />
              </button>
            )}
            <DialogTitle className="text-xl">
              {item && Object.keys(item).length > 0
                ? 'Edit Slot'
                : selectedBtn === 'all_links'
                ? 'Add new Slot'
                : selectedBtn === 'quick_link'
                ? 'Create Quick Link'
                : selectedBtn === 'link_group'
                ? 'Create Link Group'
                : selectedBtn === 'video'
                ? 'Create Video'
                : selectedBtn === 'image_upload'
                ? 'Upload Image'
                : selectedBtn === 'video_upload'
                ? 'Upload Video'
                : selectedBtn === 'slider'
                ? 'Create Slider'
                : 'Create Slot'}
            </DialogTitle>
            <CloseButton onClick={onClose}>
              <CloseIcon width={24} height={24} />
            </CloseButton>
          </header>
          <div className="w-full h-px bg-grey-5"></div>
          <main>
            {selectedBtn === 'all_links' && (
              <div className="flex flex-col gap-size2">
                <button
                  className="dbutton flex items-center gap-size0 hover:bg-grey-2 rounded-size2 p-size1 h-11"
                  onClick={() => {
                    handleSelectedBtn('quick_link');
                    setPullMetaData(false);
                  }}
                >
                  <div className="w-5 h-5">
                    <LinkIcon className="w-5 h-5" />
                  </div>
                  <span className="text-base font-regular tracking-tight">
                    Quick Link
                  </span>
                </button>
                {/* <button
                  className="flex items-center gap-size0 hover:bg-grey-2 rounded-size2 p-size1 h-11"
                  onClick={() => {
                    handleSelectedBtn('meta_link');
                    setPullMetaData(true);
                  }}
                >
                  <div className="w-5 h-5">
                    <ImageIcon className="w-4 h-4" />
                  </div>
                  <span className="text-base font-regular tracking-tight">Meta Link</span>
                </button> */}
                <button
                  className="dbutton flex items-center gap-size0 hover:bg-grey-2 rounded-size2 p-size1 h-11"
                  onClick={() => handleSelectedBtn('link_group')}
                >
                  <div className="w-5 h-5 flex items-center justify-center">
                    <ListIcon className="w-5 h-5" />
                  </div>
                  <span className="text-base font-regular tracking-tight">
                    Link Group
                  </span>
                </button>
                <button
                  className="dbutton flex items-center gap-size0 hover:bg-grey-2 rounded-size2 p-size1 h-11"
                  onClick={() => handleSelectedBtn('video')}
                >
                  <div className="w-5 h-5">
                    <VideoIcon className="w-5 h-5" />
                  </div>
                  <span className="text-base font-regular tracking-tight">
                    Video
                  </span>
                </button>
                <button
                  className="dbutton flex items-center gap-size0 hover:bg-grey-2 rounded-size2 p-size1 h-11"
                  onClick={() => handleSelectedBtn('image_upload')}
                >
                  <div className="w-5 h-5">
                    <ImageIcon className="w-5 h-5" />
                  </div>
                  <span className="text-base font-regular tracking-tight">
                    Upload Image
                  </span>
                </button>
                <button
                  className="dbutton flex items-center gap-size0 hover:bg-grey-2 rounded-size2 p-size1 h-11"
                  onClick={() => handleSelectedBtn('video_upload')}
                >
                  <div className="w-5 h-5">
                    <UploadIcon className="w-5 h-5" />
                  </div>
                  <span className="text-base font-regular tracking-tight">
                    Upload Video
                  </span>
                </button>
                <button
                  className="dbutton flex items-center gap-size0 hover:bg-grey-2 rounded-size2 p-size1 h-11"
                  onClick={() => handleSelectedBtn('slider')}
                >
                  <div className="w-5 h-5">
                    <SliderIcon className="w-5 h-5" />
                  </div>
                  <span className="text-base font-regular tracking-tight">
                    Slider
                  </span>
                </button>
              </div>
            )}

            {selectedBtn === 'quick_link' && (
              <QuickLinkForm
                setIsSaving={setIsSaving}
                closeModal={onClose}
                item={item}
                refetch={refetch}
                setSlots={setSlots}
                slots={slots}
                chatbotId={chatbotId}
                type={pullMetaData ? 'meta_link' : 'quick_link'}
              />
            )}
            {/*
            {selectedBtn === 'meta_link' && (
              <QuickLinkForm
                setIsSaving={setIsSaving}
                closeModal={onClose}
                pullMetaDataOption={true}
                item={item}
                refetch={refetch}
                setSlots={setSlots}
                slots={slots}
                chatbotId={chatbotId}
                type="meta_link"
              />
            )} */}

            {selectedBtn === 'link_group' && (
              <LinkGroupForm
                setIsSaving={setIsSaving}
                closeModal={onClose}
                item={item}
                refetch={refetch}
                setSlots={setSlots}
                slots={slots}
                chatbotId={chatbotId}
              />
            )}

            {selectedBtn === 'video' && (
              <VideoForm
                setIsSaving={setIsSaving}
                closeModal={onClose}
                item={item}
                refetch={refetch}
                setSlots={setSlots}
                slots={slots}
                chatbotId={chatbotId}
              />
            )}

            {selectedBtn === 'image_upload' && (
              <ImageUploadForm
                setIsSaving={setIsSaving}
                closeModal={onClose}
                item={item}
                setSlots={setSlots}
                slots={slots}
                chatbotId={chatbotId}
              />
            )}

            {selectedBtn === 'video_upload' && (
              <VideoUploadForm
                setIsSaving={setIsSaving}
                closeModal={onClose}
                item={item}
                setSlots={setSlots}
                slots={slots}
                chatbotId={chatbotId}
              />
            )}

            {selectedBtn === 'slider' && (
              <SliderForm
                setIsSaving={setIsSaving}
                closeModal={onClose}
                item={item}
                slots={slots}
                setSlots={setSlots}
                refetch={refetch}
                chatbotId={chatbotId}
              />
            )}
          </main>
        </DialogPanel>
      </div>
    </Dialog>
  );
};

export default SlotForm;
